#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from typing import Dict, List, Optional

class AzureCosmosDataDeleter:
    """Azure Cosmos DB 資料刪除管理類別"""

    def __init__(self, endpoint: str, key: str, database_name: str):
        """
        初始化 Azure Cosmos DB 資料刪除管理器

        Args:
            endpoint: Azure Cosmos DB 端點
            key: 存取金鑰
            database_name: 資料庫名稱
        """
        self.endpoint = endpoint
        self.key = key
        self.database_name = database_name
        self.client = CosmosClient(endpoint, key)
        self.database = None
        self.containers = {}  # 儲存已連接的容器參考

    def connect_to_database(self) -> bool:
        """
        連接到資料庫

        Returns:
            bool: 連接成功返回 True，失敗返回 False
        """
        try:
            self.database = self.client.get_database_client(self.database_name)
            print(f"✅ 資料庫 '{self.database_name}' 連接成功")
            return True
        except exceptions.CosmosHttpResponseError as e:
            print(f"❌ 連接資料庫時發生錯誤: {e.message}")
            return False

    def list_all_containers(self) -> List[str]:
        """
        列出資料庫中所有的容器

        Returns:
            容器名稱列表
        """
        try:
            containers = list(self.database.list_containers())
            container_names = [container['id'] for container in containers]

            print(f"\n📋 資料庫 '{self.database_name}' 中的容器:")
            print("-" * 60)
            for i, name in enumerate(container_names, 1):
                print(f"{i}. {name}")
            print("-" * 60)

            return container_names
        except exceptions.CosmosHttpResponseError as e:
            print(f"❌ 列出容器時發生錯誤: {e.message}")
            return []

    def get_container_client(self, container_name: str):
        """
        取得容器客戶端

        Args:
            container_name: 容器名稱

        Returns:
            容器客戶端或 None
        """
        try:
            container = self.database.get_container_client(container_name)
            self.containers[container_name] = container
            return container
        except exceptions.CosmosHttpResponseError as e:
            print(f"❌ 取得容器 '{container_name}' 時發生錯誤: {e.message}")
            return None

    def count_items_in_container(self, container_name: str) -> int:
        """
        計算容器中的資料筆數

        Args:
            container_name: 容器名稱

        Returns:
            資料筆數
        """
        container = self.get_container_client(container_name)
        if not container:
            return 0

        try:
            # 使用 COUNT 查詢計算總筆數
            query = "SELECT VALUE COUNT(1) FROM c"
            items = list(container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))
            count = items[0] if items else 0
            print(f"📊 容器 '{container_name}' 包含 {count} 筆資料")
            return count
        except Exception as e:
            print(f"❌ 計算容器 '{container_name}' 資料筆數時發生錯誤: {e}")
            return 0

    def delete_all_items_in_container(self, container_name: str) -> bool:
        """
        刪除指定容器中的所有資料（保留容器）

        Args:
            container_name: 容器名稱

        Returns:
            bool: 刪除成功返回 True
        """
        container = self.get_container_client(container_name)
        if not container:
            return False

        try:
            print(f"\n🗑️  開始刪除容器 '{container_name}' 中的所有資料...")

            # 先計算總筆數
            total_count = self.count_items_in_container(container_name)
            if total_count == 0:
                print(f"ℹ️  容器 '{container_name}' 中沒有資料需要刪除")
                return True

            # 查詢所有資料的 id 和 partition key
            query = "SELECT c.id, c.年度 FROM c"
            items = list(container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))

            deleted_count = 0
            failed_count = 0

            for item in items:
                try:
                    # 刪除每一筆資料
                    container.delete_item(
                        item=item['id'],
                        partition_key=item.get('年度', 'Unknown')
                    )
                    deleted_count += 1

                    # 每刪除 10 筆顯示一次進度
                    if deleted_count % 10 == 0:
                        print(f"   已刪除 {deleted_count}/{total_count} 筆資料...")

                except Exception as delete_error:
                    failed_count += 1
                    print(f"   ⚠️  刪除資料 {item['id']} 失敗: {delete_error}")

            print(f"✅ 容器 '{container_name}' 資料刪除完成")
            print(f"   成功刪除: {deleted_count} 筆")
            if failed_count > 0:
                print(f"   刪除失敗: {failed_count} 筆")

            return failed_count == 0

        except Exception as e:
            print(f"❌ 刪除容器 '{container_name}' 資料時發生錯誤: {e}")
            return False

    def delete_container(self, container_name: str) -> bool:
        """
        刪除指定容器（包含容器本身和所有資料）

        Args:
            container_name: 容器名稱

        Returns:
            bool: 刪除成功返回 True
        """
        try:
            print(f"\n🗑️  開始刪除容器 '{container_name}' （包含容器本身和所有資料）...")

            # 先計算總筆數
            total_count = self.count_items_in_container(container_name)
            print(f"   容器包含 {total_count} 筆資料")

            # 刪除整個容器
            self.database.delete_container(container_name)

            print(f"✅ 容器 '{container_name}' 已完全刪除")

            # 從本地容器字典中移除
            if container_name in self.containers:
                del self.containers[container_name]

            return True

        except exceptions.CosmosResourceNotFoundError:
            print(f"⚠️  容器 '{container_name}' 不存在")
            return False
        except Exception as e:
            print(f"❌ 刪除容器 '{container_name}' 時發生錯誤: {e}")
            return False

    def delete_all_containers(self) -> Dict[str, bool]:
        """
        刪除資料庫中所有容器（包含容器本身和所有資料）

        Returns:
            各容器刪除結果的字典
        """
        container_names = self.list_all_containers()
        if not container_names:
            print("❌ 沒有找到任何容器")
            return {}

        results = {}

        print(f"\n🗑️  開始刪除資料庫 '{self.database_name}' 中的所有容器（包含容器本身和所有資料）...")
        print("=" * 80)

        for container_name in container_names:
            print(f"\n處理容器: {container_name}")
            results[container_name] = self.delete_container(container_name)

        return results

    def delete_specific_containers_with_container(self, container_names: List[str]) -> Dict[str, bool]:
        """
        刪除指定容器（包含容器本身和所有資料）

        Args:
            container_names: 要刪除的容器名稱列表

        Returns:
            各容器刪除結果的字典
        """
        results = {}

        print(f"\n🗑️  開始刪除以下容器（包含容器本身和所有資料）：")
        for name in container_names:
            print(f"  - {name}")
        print("=" * 60)

        for container_name in container_names:
            print(f"\n處理容器: {container_name}")
            results[container_name] = self.delete_container(container_name)

        return results

    def delete_specific_containers_data_only(self, container_names: List[str]) -> Dict[str, bool]:
        """
        刪除指定容器中的所有資料（保留容器）

        Args:
            container_names: 要刪除資料的容器名稱列表

        Returns:
            各容器刪除結果的字典
        """
        results = {}

        print(f"\n🗑️  開始刪除以下容器的所有資料（保留容器）：")
        for name in container_names:
            print(f"  - {name}")
        print("=" * 60)

        for container_name in container_names:
            print(f"\n處理容器: {container_name}")
            results[container_name] = self.delete_all_items_in_container(container_name)

        return results

    def show_database_summary(self):
        """顯示資料庫摘要資訊"""
        container_names = self.list_all_containers()

        print(f"\n📊 資料庫 '{self.database_name}' 摘要:")
        print("=" * 60)

        total_items = 0
        for container_name in container_names:
            count = self.count_items_in_container(container_name)
            total_items += count

        print(f"總容器數: {len(container_names)}")
        print(f"總資料筆數: {total_items}")
        print("=" * 60)


def main():
    """主程式"""
    # 連線資訊配置（與 AzureCosmos.py 相同）
    ENDPOINT = "https://yuntech-japan-nosql.documents.azure.com:443/"
    KEY = "****************************************************************************************"
    DATABASE_NAME = "Dashboard"

    # 建立 Cosmos DB 資料刪除管理器
    deleter = AzureCosmosDataDeleter(ENDPOINT, KEY, DATABASE_NAME)

    try:
        # 1. 連接到資料庫
        if not deleter.connect_to_database():
            return

        # 2. 顯示資料庫摘要
        deleter.show_database_summary()

        # 3. 選擇操作模式
        print("\n請選擇操作模式:")
        print("1. 刪除所有容器含資料")
        print("2. 刪除指定容器含資料")
        print("3. 刪除指定容器內資料")
        print("4. 退出")

        choice = input("\n請輸入選項 (1-4): ").strip()

        if choice == "1":
            # 刪除所有容器含資料
            deleter.delete_all_containers()

        elif choice == "2":
            # 刪除指定容器含資料
            container_names = deleter.list_all_containers()
            if container_names:
                print("\n請輸入要刪除的容器名稱（用逗號分隔）:")
                selected = input().strip().split(',')
                selected = [name.strip() for name in selected if name.strip()]

                if selected:
                    deleter.delete_specific_containers_with_container(selected)
                else:
                    print("❌ 沒有選擇任何容器")

        elif choice == "3":
            # 刪除指定容器內資料
            container_names = deleter.list_all_containers()
            if container_names:
                print("\n請輸入要刪除資料的容器名稱（用逗號分隔）:")
                selected = input().strip().split(',')
                selected = [name.strip() for name in selected if name.strip()]

                if selected:
                    deleter.delete_specific_containers_data_only(selected)
                else:
                    print("❌ 沒有選擇任何容器")

        elif choice == "4":
            print("👋 程式結束")

        else:
            print("❌ 無效的選項")

        print("\n🎉 操作完成！")

    except exceptions.CosmosHttpResponseError as e:
        print(f"❌ Cosmos DB 操作錯誤: {e.message}")
    except Exception as e:
        print(f"❌ 發生未預期的錯誤: {e}")


if __name__ == "__main__":
    main()
