import os
from pathlib import Path

def 尋找資料夾(target_dir, folder_keywords=None):
    """
    在指定目錄下尋找符合關鍵字的資料夾
    """
    found_folders = []
    
    try:
        print(f"正在搜尋目錄: {target_dir}")
        items = os.listdir(target_dir)
        print(f"目錄中的項目: {items}")
        
        for item in items:
            full_path = Path(target_dir) / item
            print(f"檢查項目: {item}, 是資料夾: {full_path.is_dir()}")
            
            if full_path.is_dir():
                # 如果沒有指定關鍵字，或者資料夾名符合任一關鍵字
                if folder_keywords is None:
                    found_folders.append(full_path)
                    print(f"添加資料夾 (無關鍵字): {full_path}")
                else:
                    for keyword in folder_keywords:
                        if keyword in item:
                            found_folders.append(full_path)
                            print(f"添加資料夾 (符合關鍵字 '{keyword}'): {full_path}")
                            break
    except Exception as e:
        print(f"搜尋資料夾時發生錯誤: {e}")
    
    return found_folders

def 尋找檔案(target_dir, file_keywords=None, extensions=None):
    """
    在指定目錄下尋找符合關鍵字和副檔名的檔案
    """
    found_files = []
    
    try:
        print(f"正在搜尋檔案於目錄: {target_dir}")
        items = os.listdir(target_dir)
        print(f"目錄中的項目: {items}")
        
        for item in items:
            full_path = Path(target_dir) / item
            print(f"檢查檔案: {item}, 是檔案: {full_path.is_file()}")
            
            if full_path.is_file():
                # 忽略已整合的檔案
                if "已整合-" in item:
                    print(f"忽略已整合檔案: {item}")
                    continue
                
                # 忽略臨時檔案（以 ~$ 開頭）
                if item.startswith("~$"):
                    print(f"忽略臨時檔案: {item}")
                    continue
                
                # 忽略隱藏檔案（以 . 開頭）
                if item.startswith("."):
                    print(f"忽略隱藏檔案: {item}")
                    continue
                
                # 檢查副檔名
                ext_match = extensions is None or full_path.suffix in extensions
                print(f"副檔名檢查 - 檔案: {item}, 副檔名: {full_path.suffix}, 要求: {extensions}, 符合: {ext_match}")
                
                # 檢查關鍵字
                keyword_match = file_keywords is None or any(keyword in item for keyword in file_keywords)
                print(f"關鍵字檢查 - 檔案: {item}, 關鍵字: {file_keywords}, 符合: {keyword_match}")
                
                if ext_match and keyword_match:
                    found_files.append(full_path)
                    print(f"添加檔案: {full_path}")
    except Exception as e:
        print(f"搜尋檔案時發生錯誤: {e}")
    
    return found_files

if __name__ == "__main__":
    # 基礎目錄
    base_directory = r"C:\Users\<USER>\Desktop\大專爬蟲"
    base_path = Path(base_directory)
    
    print(f"基礎目錄: {base_directory}")
    print(f"基礎目錄存在: {base_path.exists()}")
    print(f"基礎目錄是資料夾: {base_path.is_dir()}")
    
    # 尋找財務資料夾
    target_folders = 尋找資料夾(base_path, ['財務'])
    print(f"找到的財務資料夾: {target_folders}")
    
    if target_folders:
        folder = target_folders[0]
        print(f"正在檢查資料夾: {folder}")
        
        # 尋找國立學校可用資金檔案
        csv_files = 尋找檔案(folder, ['國立學校可用資金'], ['.csv'])
        print(f"找到的CSV檔案: {csv_files}")
