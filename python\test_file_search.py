#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試檔案搜尋功能
"""

from pathlib import Path
import os

def 測試檔案搜尋():
    """測試檔案搜尋功能"""
    print("🔍 測試檔案搜尋功能")
    print("="*40)
    
    # 測試財務資料夾
    財務資料夾 = Path(r"C:\Users\<USER>\Desktop\大專爬蟲\財務")
    
    if not 財務資料夾.exists():
        print(f"❌ 財務資料夾不存在: {財務資料夾}")
        return
    
    print(f"📁 檢查資料夾: {財務資料夾}")
    
    # 列出所有檔案
    print("\n📄 資料夾中的所有檔案:")
    try:
        for item in os.listdir(財務資料夾):
            full_path = 財務資料夾 / item
            if full_path.is_file():
                print(f"  - {item}")
    except Exception as e:
        print(f"❌ 列出檔案時發生錯誤: {e}")
        return
    
    # 測試關鍵字匹配
    關鍵字 = "國立學校可用資金"
    print(f"\n🔍 搜尋關鍵字: '{關鍵字}'")
    
    found_files = []
    try:
        for item in os.listdir(財務資料夾):
            full_path = 財務資料夾 / item
            
            if full_path.is_file():
                print(f"  檢查檔案: {item}")
                
                # 忽略已整合的檔案
                if "已整合-" in item:
                    print(f"    ⏭️  跳過已整合檔案")
                    continue
                
                # 檢查副檔名
                if full_path.suffix != '.csv':
                    print(f"    ⏭️  跳過非CSV檔案")
                    continue
                
                # 檢查關鍵字
                if 關鍵字 in item:
                    print(f"    ✅ 關鍵字匹配!")
                    found_files.append(full_path)
                else:
                    print(f"    ❌ 關鍵字不匹配")
                    
    except Exception as e:
        print(f"❌ 搜尋時發生錯誤: {e}")
    
    print(f"\n📊 搜尋結果:")
    print(f"找到 {len(found_files)} 個符合的檔案:")
    for file in found_files:
        print(f"  ✅ {file.name}")
    
    # 測試導入資料清理模組
    print(f"\n🧪 測試資料清理模組的搜尋功能:")
    try:
        from 資料清理 import 尋找檔案
        
        result = 尋找檔案(財務資料夾, [關鍵字], ['.csv'])
        print(f"模組搜尋結果: {len(result)} 個檔案")
        for file in result:
            print(f"  ✅ {file.name}")
            
    except Exception as e:
        print(f"❌ 測試模組時發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    測試檔案搜尋()
