import requests
from bs4 import BeautifulSoup
import os
import re
import concurrent.futures
import time

# 嘗試導入 tqdm，如果沒有安裝則使用簡單的進度顯示
try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    def tqdm(iterable, total=None, desc="", unit=""):
        """簡單的進度顯示替代"""
        for i, item in enumerate(iterable, 1):
            if total:
                print(f"\r{desc}: {i}/{total} {unit}", end="", flush=True)
            yield item
        print()  # 換行

# Step 1: 設定基本資料
base_url = 'https://udb.moe.edu.tw'
baseUrl = 'https://udb.moe.edu.tw/udata/DetailReportList'

# 5個分類頁面的URL配置
category_pages = {
    '學生': {
        'url': f'{baseUrl}/學生類',
        'download_items': {
            '在學學生數': '學1-1.正式學籍在學學生人數-以「系(所)」統計',
            '外國學生數': '學3-2.外國學生數及其在學比率-以「系(所)」統計',
        }
    },
    '教職': {
        'url': f'{baseUrl}/教職類',
        'download_items': {
            '學年度專任教師數': '教1-2.專任教師數-以「校」統計',
            '外籍專任教師數': '教3-2.外籍專任教師數-以「校」統計',
            '日間生師比': '教5.日間學制生師比-以「校」統計',
            '編制外專任教師比率': '教11.編制外專任教師數及其比率-以「校」統計#(112學年度起)',
        }
    },
    '研究': {
        'url': f'{baseUrl}/研究類',
        'download_items': {
            '產學合作計畫經費': '研2.學校承接各單位資助「產學合作」計畫經費及其每師平均承接金額-以「校」統計',
            '學校承接各單位資助計畫經費': '研1.學校承接各單位資助「各類計畫經費」及其每師平均承接金額-以「校」統計',
        }
    },
    '校務': {
        'url': f'{baseUrl}/校務類',
        'download_items': {
            '圖書館': '校5.圖書館統計-以「校」統計',
            '學校提供一年級學生住宿之比率': '校9.提供一年級學生住宿人數及其比率-以「校」統計',
            '學校開設全外語授課之院': '校15.學校開設全外語授課之院、系所、學位學程-以「系(所)」統計',
        }
    },
    '財務': {
        'url': f'{baseUrl}/財務類',
        'download_items': {
            '國立學校可用資金': '財1-1.國立學校可用資金、本年度現金增減情形-以「校」統計',
            '學雜費收入占總收入之比率': '財1-2.國立學校學雜費收入占總收入比率-以「校」統計',
        }
    }
}

# 設定主要資料夾名稱
MAIN_FOLDER_NAME = "大專爬蟲"

# 設定最大同時下載數
MAX_WORKERS = 10
# 設定請求間隔 (秒)
REQUEST_DELAY = 0.5

def 獲取分類頁面內容(category_url, headers):
    """獲取分類頁面的HTML內容"""
    try:
        response = requests.get(category_url, headers=headers)
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        return soup
    except Exception as e:
        print(f"獲取頁面內容時發生錯誤: {str(e)}")
        return None

def 尋找CSV下載連結(soup, target_text):
    """在頁面中尋找指定文字的CSV下載連結"""
    try:
        # 尋找包含目標文字的<a>標籤（使用title屬性搜索）
        target_links = soup.find_all('a', title=lambda title: title and target_text in title)

        if not target_links:
            print(f"找不到title包含 '{target_text}' 的連結")
            # 如果title搜索失敗，嘗試用文字內容搜索作為備用方案
            target_links = soup.find_all('a', string=lambda text: text and target_text in text.strip())
            if not target_links:
                print(f"找不到包含 '{target_text}' 的連結")
                return None

        # 找到目標連結後，尋找同一行的CSV下載按鈕
        for link in target_links:
            # 找到包含此連結的<tr>元素
            tr_element = link.find_parent('tr')
            if tr_element:
                # 在這個<tr>中尋找CSV下載連結
                csv_link = tr_element.find('a', href=lambda href: href and '.csv' in href)
                if csv_link:
                    csv_url = csv_link.get('href')
                    # 如果是相對路徑，轉換為絕對路徑
                    if csv_url.startswith('/'):
                        csv_url = base_url + csv_url
                    return csv_url

        print(f"找到 '{target_text}' 連結，但找不到對應的CSV下載連結")
        return None

    except Exception as e:
        print(f"尋找CSV下載連結時發生錯誤: {str(e)}")
        return None

def 下載CSV檔案(csv_url, headers, output_path, retry_count=0, max_retries=3):
    """下載CSV檔案並轉換為帶BOM的UTF-8編碼"""
    time.sleep(REQUEST_DELAY)  # 避免請求過於頻繁
    try:
        response = requests.get(csv_url, headers=headers)
        response.raise_for_status()

        # 確保目錄存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 先將內容解碼為UTF-8，然後重新編碼為帶BOM的UTF-8
        try:
            # 嘗試以UTF-8解碼
            content_text = response.content.decode('utf-8')
        except UnicodeDecodeError:
            # 如果UTF-8解碼失敗，嘗試其他編碼
            try:
                content_text = response.content.decode('utf-8-sig')
            except UnicodeDecodeError:
                content_text = response.content.decode('big5')

        # 寫入檔案時使用帶BOM的UTF-8編碼
        with open(output_path, 'w', encoding='utf-8-sig', newline='') as f:
            f.write(content_text)

        return output_path
    except (requests.exceptions.RequestException, IOError, UnicodeDecodeError) as e:
        if retry_count < max_retries:
            retry_delay = REQUEST_DELAY * (2 ** retry_count)
            error_type = type(e).__name__
            print(f"下載CSV檔案時發生 {error_type} 錯誤，{retry_delay}秒後進行第{retry_count+1}次重試...")
            time.sleep(retry_delay)
            return 下載CSV檔案(csv_url, headers, output_path, retry_count + 1, max_retries)
        else:
            raise Exception(f"下載CSV檔案失敗，已重試{retry_count}次: {str(e)}")

def 處理單一下載任務(task_info, retry_count=0, max_retries=3):
    """處理單一CSV下載任務"""
    category = task_info['category']
    item_name = task_info['item_name']
    target_text = task_info['target_text']
    category_url = task_info['category_url']
    headers = task_info['headers']

    try:
        # 1. 獲取分類頁面內容
        soup = 獲取分類頁面內容(category_url, headers)
        if not soup:
            raise Exception(f"無法獲取 {category} 頁面內容")

        # 2. 尋找CSV下載連結
        csv_url = 尋找CSV下載連結(soup, target_text)
        if not csv_url:
            raise Exception(f"找不到 '{target_text}' 的CSV下載連結")
        # 3. 設定輸出路徑
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_folder = os.path.join(desktop_path, MAIN_FOLDER_NAME)
        category_folder = os.path.join(main_folder, category)

        # 檔名使用 download_items 的 key，移除不適合檔名的字符
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', item_name)
        output_filename = f"{safe_filename}.csv"
        output_path = os.path.join(category_folder, output_filename)

        # 4. 下載CSV檔案
        final_path = 下載CSV檔案(csv_url, headers, output_path)

        print(f"✅ 成功下載: {final_path}")

        return {
            "category": category,
            "item_name": item_name,
            "target_text": target_text,
            "file_path": final_path,
            "csv_url": csv_url,
            "status": "success"
        }

    except Exception as error:
        # 檢查是否可以重試
        if retry_count < max_retries:
            retry_delay = REQUEST_DELAY * (2 ** retry_count)
            error_type = type(error).__name__
            print(f"下載 {category} - {item_name} 時發生 {error_type} 錯誤，{retry_delay}秒後進行第{retry_count+1}次重試...")
            time.sleep(retry_delay)
            return 處理單一下載任務(task_info, retry_count + 1, max_retries)

        # 超過最大重試次數，回報失敗
        error_msg = f"下載 {category} - {item_name} 時發生錯誤: {str(error)}"
        print(f"❌ 下載失敗 (已重試{retry_count}次): {error_msg}")

        return {
            "category": category,
            "item_name": item_name,
            "target_text": target_text,
            "status": "failed",
            "error": error_msg,
            "retry_count": retry_count
        }



def 主程序():
    """主要下載程序 - 爬取5個分類頁面並下載CSV"""
    start_time = time.time()

    # 設定請求標頭
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    download_results = []
    failed_results = []
    download_tasks = []

    # 創建主資料夾
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    main_folder = os.path.join(desktop_path, MAIN_FOLDER_NAME)
    os.makedirs(main_folder, exist_ok=True)

    # 創建分類資料夾
    for category in category_pages.keys():
        category_folder = os.path.join(main_folder, category)
        os.makedirs(category_folder, exist_ok=True)
        print(f"✅ 已創建資料夾: {category_folder}")

    print(f"\n🚀 開始準備下載任務...")
    print("="*60)

    # 準備所有下載任務
    for category, config in category_pages.items():
        category_url = config['url']
        download_items = config['download_items']

        if not download_items:
            print(f"⚠️  {category} 沒有配置下載項目，跳過")
            continue

        # 為每個下載項目創建任務
        for item_name, target_text in download_items.items():
            task = {
                'category': category,
                'item_name': item_name,
                'target_text': target_text,
                'category_url': category_url,
                'headers': headers.copy()
            }
            download_tasks.append(task)
            print(f"  ➕ 已添加任務: {item_name} -> {target_text}")

    # 顯示任務總數
    total_tasks = len(download_tasks)
    print(f"\n📊 總共 {total_tasks} 個下載任務準備就緒")
    print("="*60)

    if total_tasks == 0:
        print("❌ 沒有任何下載任務，程序結束")
        return {"success": [], "failed": []}

    # 多執行緒並行下載
    print(f"\n🔄 開始批量下載 (最大並行數: {MAX_WORKERS})...")
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 使用 tqdm 顯示進度條
        results = list(tqdm(
            executor.map(處理單一下載任務, download_tasks),
            total=total_tasks,
            desc="📥 下載進度",
            unit="個檔案"
        ))

        # 收集結果
        for result in results:
            if result:
                if result.get('status') == 'success':
                    download_results.append(result)
                else:
                    failed_results.append(result)

    # 顯示統計資訊
    success_count = len(download_results)
    failed_count = len(failed_results)

    print(f"\n" + "="*60)
    print(f"📊 下載統計結果:")
    print(f"="*60)
    print(f"總共任務數: {total_tasks}")
    print(f"✅ 成功下載: {success_count}")
    print(f"❌ 失敗下載: {failed_count}")

    # 顯示成功下載的檔案
    if success_count > 0:
        print(f"\n✅ 成功下載的檔案:")
        for i, success in enumerate(download_results, 1):
            print(f"  {i}. {success['category']} - {success['item_name']}")
            print(f"     📁 {success['file_path']}")

    # 顯示失敗項目詳細資訊
    if failed_count > 0:
        print(f"\n❌ 失敗下載項目詳細資訊:")
        for i, failed in enumerate(failed_results, 1):
            print(f"  {i}. {failed['category']} - {failed['item_name']}")
            print(f"     🎯 目標文字: {failed['target_text']}")
            print(f"     ❌ 錯誤原因: {failed.get('error', '未知錯誤')}")
            print(f"     🔄 已嘗試次數: {failed.get('retry_count', 0)}")
            print()

    # 計算執行時間
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"⏱️  總執行時間: {execution_time:.2f} 秒")
    print(f"📁 檔案已儲存至桌面的 '{MAIN_FOLDER_NAME}' 資料夾")

    return {
        "success": download_results,
        "failed": failed_results
    }

if __name__ == "__main__":
    主程序()
